/**
 * Security Configuration Utility
 * 
 * Fetches dynamic security configuration from the backend
 */

import { get } from '@/utils/apiUtils';

// Cache for security configuration
let configCache = null;
let configCacheTime = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Fetch security configuration from backend
 * @returns {Promise<Object>} Security configuration object
 */
export async function fetchSecurityConfig() {
    try {
        // Check if we have a valid cached config
        if (configCache && configCacheTime && (Date.now() - configCacheTime < CACHE_DURATION)) {
            return configCache;
        }

        const response = await get('/security-verification/config');
        
        if (response.success && response.config) {
            configCache = response.config;
            configCacheTime = Date.now();
            return response.config;
        } else {
            throw new Error('Invalid response from security config endpoint');
        }
    } catch (error) {
        console.warn('Failed to fetch security config from backend, using defaults:', error);
        
        // Return minimal default configuration if backend fails
        // NOTE: No hardcoded routes - true centralization means backend is the only source
        const defaultConfig = {
            cookie_name: 'security_verified',
            expires_in_minutes: 1,
            check_interval_seconds: 5,
            check_interval_milliseconds: 5000,
            // NO hardcoded protected routes - backend is the single source of truth
            protected_routes: [],
            // Minimal referrer control settings (only essential defaults)
            referrer_control: {
                enabled: true,
                valid_referrer_prefixes: [],
                invalid_referrer_paths: [],
                fallback_urls: {}
            }
        };
        
        configCache = defaultConfig;
        configCacheTime = Date.now();
        return defaultConfig;
    }
}

/**
 * Get cached security configuration (synchronous)
 * @returns {Object|null} Cached configuration or null if not available
 */
export function getCachedSecurityConfig() {
    if (configCache && configCacheTime && (Date.now() - configCacheTime < CACHE_DURATION)) {
        return configCache;
    }
    return null;
}

/**
 * Get protected routes from configuration
 * @returns {Promise<Array>} Array of protected route paths
 */
export async function getProtectedRoutes() {
    try {
        const config = await fetchSecurityConfig();
        return config.protected_routes || [];
    } catch (error) {
        console.warn('Failed to fetch protected routes, using defaults:', error);
        return ['/account/phone/setup']; // Fallback
    }
}

/**
 * Get cached protected routes (synchronous)
 * @returns {Array} Array of protected route paths or fallback
 */
export function getCachedProtectedRoutes() {
    const config = getCachedSecurityConfig();
    if (config && config.protected_routes) {
        return config.protected_routes;
    }
    // Return fallback if no cached config
    return ['/account/phone/setup'];
}

/**
 * Get referrer control configuration
 * @returns {Promise<Object>} Referrer control configuration
 */
export async function getReferrerControlConfig() {
    try {
        const config = await fetchSecurityConfig();
        return config.referrer_control || {};
    } catch (error) {
        console.warn('Failed to fetch referrer control config, using defaults:', error);
        return {
            enabled: true,
            valid_referrer_prefixes: ['/account', '/dashboard', '/user'],
            invalid_referrer_paths: ['/login', '/signup', '/security-check'],
            fallback_urls: {}
        };
    }
}

/**
 * Check if a path is a valid secure route
 * @param {string} path - The path to validate
 * @returns {boolean} True if the path is a protected route
 */
export function isValidSecureRoute(path, protectedRoutes = null) {
    try {
        // Use provided routes or get from cache
        const routes = protectedRoutes || getCachedProtectedRoutes();

        // Decode URL if it's encoded
        const decodedPath = decodeURIComponent(path);

        // Extract just the path part (remove query parameters for validation)
        const url = new URL(decodedPath, 'http://localhost');
        const pathOnly = url.pathname;

        // Remove leading slash if present for consistent comparison
        const normalizedPath = pathOnly.replace(/^\//, '');

        return routes.some(route => {
            // Remove leading slash from secure route for comparison
            const normalizedRoute = route.replace(/^\//, '');

            // Check if the path matches exactly or starts with the secure route path
            return normalizedPath === normalizedRoute || normalizedPath.startsWith(normalizedRoute);
        });
    } catch (error) {
        console.warn('Error validating secure route:', error);
        return false;
    }
}

/**
 * Clear the configuration cache
 */
export function clearSecurityConfigCache() {
    configCache = null;
    configCacheTime = null;
}

/**
 * Get default security configuration
 * @returns {Object} Default configuration object
 */
export function getDefaultSecurityConfig() {
    return {
        cookie_name: 'security_verified',
        expires_in_minutes: 1,
        check_interval_seconds: 5,
        check_interval_milliseconds: 5000,
    };
}
