/**
 * Security Configuration Debug Utility
 * 
 * This utility helps verify that the centralized security configuration is working properly.
 * Use this to test that all routes are coming from the backend and no hardcoded values exist.
 */

import { 
    fetchSecurityConfig, 
    getCachedSecurityConfig, 
    getProtectedRoutes, 
    getCachedProtectedRoutes,
    isSecurityConfigLoaded,
    initializeSecurityConfig
} from './securityConfig';

/**
 * Debug the current security configuration state
 * @returns {Promise<Object>} Debug information about the configuration
 */
export async function debugSecurityConfig() {
    console.log('🔍 [SecurityConfigDebug] Starting security configuration debug...');
    
    const debugInfo = {
        timestamp: new Date().toISOString(),
        configLoaded: false,
        cachedConfig: null,
        freshConfig: null,
        protectedRoutes: {
            cached: [],
            fresh: []
        },
        issues: [],
        recommendations: []
    };

    try {
        // Check if config is already loaded
        debugInfo.configLoaded = isSecurityConfigLoaded();
        debugInfo.cachedConfig = getCachedSecurityConfig();
        
        // Get cached protected routes
        debugInfo.protectedRoutes.cached = getCachedProtectedRoutes();
        
        // Fetch fresh config from backend
        try {
            debugInfo.freshConfig = await fetchSecurityConfig();
            debugInfo.protectedRoutes.fresh = await getProtectedRoutes();
        } catch (error) {
            debugInfo.issues.push(`Failed to fetch fresh config: ${error.message}`);
        }

        // Analyze the configuration
        analyzeConfiguration(debugInfo);
        
        // Log results
        logDebugResults(debugInfo);
        
        return debugInfo;
        
    } catch (error) {
        debugInfo.issues.push(`Debug failed: ${error.message}`);
        console.error('🚨 [SecurityConfigDebug] Debug failed:', error);
        return debugInfo;
    }
}

/**
 * Analyze the configuration for issues and recommendations
 */
function analyzeConfiguration(debugInfo) {
    // Check if any hardcoded routes exist (should be none)
    if (debugInfo.protectedRoutes.cached.length === 0 && !debugInfo.configLoaded) {
        debugInfo.issues.push('No cached configuration available - backend may be unreachable');
        debugInfo.recommendations.push('Ensure backend is running and accessible');
    }
    
    // Check if fresh config differs from cached
    if (debugInfo.freshConfig && debugInfo.cachedConfig) {
        const cachedRoutes = JSON.stringify(debugInfo.protectedRoutes.cached.sort());
        const freshRoutes = JSON.stringify(debugInfo.protectedRoutes.fresh.sort());
        
        if (cachedRoutes !== freshRoutes) {
            debugInfo.issues.push('Cached and fresh configurations differ');
            debugInfo.recommendations.push('Configuration may have been updated - consider refreshing cache');
        }
    }
    
    // Check for centralization success
    if (debugInfo.protectedRoutes.fresh.length > 0) {
        debugInfo.recommendations.push('✅ Centralization working - routes loaded from backend');
    } else if (debugInfo.protectedRoutes.cached.length > 0) {
        debugInfo.recommendations.push('⚠️ Using cached routes - backend may be temporarily unavailable');
    } else {
        debugInfo.issues.push('❌ No protected routes available from any source');
        debugInfo.recommendations.push('Check backend configuration in config/security.php');
    }
}

/**
 * Log debug results to console
 */
function logDebugResults(debugInfo) {
    console.log('📊 [SecurityConfigDebug] Configuration Analysis:');
    console.log('  Config Loaded:', debugInfo.configLoaded);
    console.log('  Cached Routes:', debugInfo.protectedRoutes.cached);
    console.log('  Fresh Routes:', debugInfo.protectedRoutes.fresh);
    
    if (debugInfo.issues.length > 0) {
        console.warn('⚠️ [SecurityConfigDebug] Issues found:');
        debugInfo.issues.forEach(issue => console.warn(`  - ${issue}`));
    }
    
    if (debugInfo.recommendations.length > 0) {
        console.log('💡 [SecurityConfigDebug] Recommendations:');
        debugInfo.recommendations.forEach(rec => console.log(`  - ${rec}`));
    }
}

/**
 * Test centralization by verifying no hardcoded routes exist
 * @returns {Promise<boolean>} True if centralization is working properly
 */
export async function testCentralization() {
    console.log('🧪 [SecurityConfigDebug] Testing centralization...');
    
    try {
        // Initialize config
        const initialized = await initializeSecurityConfig();
        
        if (!initialized) {
            console.error('❌ [SecurityConfigDebug] Failed to initialize configuration');
            return false;
        }
        
        // Get routes from centralized source
        const routes = await getProtectedRoutes();
        
        if (routes.length === 0) {
            console.warn('⚠️ [SecurityConfigDebug] No protected routes configured');
            return false;
        }
        
        console.log('✅ [SecurityConfigDebug] Centralization test passed');
        console.log(`   Protected routes: ${routes.join(', ')}`);
        console.log('   Source: Backend config/security.php');
        
        return true;
        
    } catch (error) {
        console.error('❌ [SecurityConfigDebug] Centralization test failed:', error);
        return false;
    }
}

/**
 * Quick status check for security configuration
 * @returns {string} Status message
 */
export function getConfigStatus() {
    const isLoaded = isSecurityConfigLoaded();
    const cachedRoutes = getCachedProtectedRoutes();
    
    if (isLoaded && cachedRoutes.length > 0) {
        return `✅ Config loaded: ${cachedRoutes.length} protected routes`;
    } else if (cachedRoutes.length > 0) {
        return `⚠️ Using cached config: ${cachedRoutes.length} routes`;
    } else {
        return '❌ No configuration available';
    }
}
