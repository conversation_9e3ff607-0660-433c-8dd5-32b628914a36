<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Security Verification Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the security verification
    | system including cookie settings, expiration times, and security options.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Cookie Configuration
    |--------------------------------------------------------------------------
    |
    | Basic cookie settings with sensible defaults and minimal customization.
    |
    */

    'cookie' => [
        // Cookie name for security verification
        'name' => 'security_verified',

        // Cookie expiration time in minutes
        'expires_minutes' => 5,

        // Cookie monitoring check interval in seconds
        'check_interval_seconds' => 1,

        // Cookie path
        'path' => '/',

        // HTTP Only flag (false so frontend can read it)
        'http_only' => !(env('APP_ENV') == 'local'),

        // Secure flag (auto-detected based on environment)
        'secure' => !(env('APP_ENV') == 'local'), // null = auto-detect, true = force HTTPS, false = allow HTTP

        // SameSite attribute
        'same_site' => 'Lax',
    ],

    /*
    |--------------------------------------------------------------------------
    | Cryptographic Security
    |--------------------------------------------------------------------------
    |
    | Enable cryptographic signing to prevent cookie tampering.
    |
    */

    'crypto' => [
        // Enable cryptographic signing of cookie values
        'sign_cookies' => true,

        // Algorithm for signing
        'signing_algorithm' => 'sha256',
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Configuration
    |--------------------------------------------------------------------------
    |
    | Basic session and rate limiting settings.
    |
    */

    'session' => [
        // Verification session cache expiration in minutes
        'cache_expires_minutes' => 15,

        // Rate limiting for resend requests in seconds
        'resend_rate_limit_seconds' => 60,

        // Maximum verification attempts before lockout
        'max_attempts' => 5,

        // Lockout duration in minutes after max attempts
        'lockout_minutes' => 15,
    ],

    /*
    |--------------------------------------------------------------------------
    | Protected Routes
    |--------------------------------------------------------------------------
    |
    | List of routes that require security verification.
    |
    */

    'protected_routes' => [
        '/account/phone/setup',
        '/account/email/setup',
        '/account/password/change',
        '/account/2fa/setup',
        '/account/create-username',
        '/account/address/manage',
        '/account/address/setup',
    ],

    /*
    |--------------------------------------------------------------------------
    | Referrer-Based Access Control
    |--------------------------------------------------------------------------
    |
    | Configuration for preventing direct URL access to secure pages.
    |
    */

    'referrer_control' => [
        // Enable referrer-based access control
        'enabled' => true,

        // Valid referrer path prefixes (pages users can navigate from)
        'valid_referrer_prefixes' => [
            '/account',
            '/dashboard',
            '/user',
            '/marketplace',
            '/pricing',
            '/help',
            '/settings',
            '/', // Home page
        ],

        // Invalid referrer paths (pages users should not navigate from)
        'invalid_referrer_paths' => [
            '/login',
            '/signup',
            '/forget-password',
            '/verify-email',
            '/security-check',
            '/logout',
        ],

        // Fallback URL mappings for direct access prevention
        'fallback_urls' => [
            '/account/phone/setup' => '/account/overview',
            '/account/email/setup' => '/account/overview',
            '/account/password/change' => '/account/overview',
            '/account/2fa/setup' => '/account/overview',
            '/account/create-username' => '/account/overview',
            '/account/address/manage' => '/account/overview',
            '/account/address/setup' => '/account/address/manage',
        ],

        // Default fallback URL when no specific mapping exists
        'default_fallback' => '/account/overview',
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment Detection
    |--------------------------------------------------------------------------
    |
    | Simple environment detection for cookie domain and security settings.
    |
    */

    'environment' => [
        // Development domains (for auto-detection)
        'development_domains' => [
            'localhost',
            '127.0.0.1',
            '::1',
            '.local',
            '.dev',
            '.test',
        ],

        // Production domains (for auto-detection)
        'production_domains' => [
            'tradereply.com',
            '.tradereply.com',
        ],
    ],

];
